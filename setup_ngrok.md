# Ngrok Setup for KFT Fitness PWA

## Quick Setup Guide

### 1. Install ngrok
```bash
# Download from https://ngrok.com/download
# Or install via package manager:

# macOS
brew install ngrok/ngrok/ngrok

# Windows (Chocolatey)
choco install ngrok

# Linux (Snap)
sudo snap install ngrok
```

### 2. Setup ngrok account
```bash
# Sign up at https://ngrok.com and get your auth token
ngrok config add-authtoken YOUR_AUTH_TOKEN
```

### 3. Start your backend server
```bash
# Navigate to your backend directory (admin folder)
cd admin

# Start PHP server (example)
php -S localhost:8080

# Or if using Apache/Nginx, make sure it's running on port 8080
```

### 4. Start ngrok tunnel
```bash
# In a new terminal, start ngrok
ngrok http 8080

# You'll see output like:
# Forwarding  https://abc123.ngrok.io -> http://localhost:8080
```

### 5. Update Flutter app configuration

#### Option A: Update the config file
Edit `lib/config/ngrok_config.dart`:
```dart
static const String ngrokUrl = 'https://your-actual-ngrok-url.ngrok.io/admin/api/';
```

#### Option B: Use runtime configuration
In your Flutter app, you can also update the URL at runtime:
```dart
// In main.dart or anywhere in your app
NgrokConfig.updateNgrokUrl('https://abc123.ngrok.io');
```

### 6. Test the connection
```bash
# Test if your ngrok URL is working
curl https://your-ngrok-url.ngrok.io/admin/api/health

# Or test in browser
open https://your-ngrok-url.ngrok.io/admin/
```

## Quick Commands

### Start everything:
```bash
# Terminal 1: Start backend
cd admin && php -S localhost:8080

# Terminal 2: Start ngrok
ngrok http 8080

# Terminal 3: Start Flutter web
cd /path/to/flutter/app
flutter run -d chrome --web-port 3000
```

### Update Flutter app with new ngrok URL:
```dart
// Method 1: Update config file
// Edit lib/config/ngrok_config.dart

// Method 2: Runtime update
ApiService.useNgrok('https://new-url.ngrok.io');
```

## Troubleshooting

### Common Issues:

1. **Ngrok URL not working**
   - Check if backend server is running on localhost:8080
   - Verify ngrok is forwarding to the correct port
   - Check firewall settings

2. **CORS errors**
   - Make sure your backend allows ngrok domain
   - Add ngrok URL to CORS allowed origins

3. **SSL/HTTPS issues**
   - Ngrok provides HTTPS by default
   - Make sure your API endpoints support HTTPS

### Useful ngrok commands:
```bash
# View active tunnels
ngrok tunnels list

# Start with custom subdomain (paid feature)
ngrok http 8080 --subdomain=kft-fitness

# Start with basic auth
ngrok http 8080 --basic-auth="username:password"

# View web interface
open http://localhost:4040
```

## Production Deployment

When ready for production:

1. Update `NgrokConfig.productionUrl` with your actual domain
2. Use `NgrokConfig.initializeWithProduction()` 
3. Deploy your backend to a proper hosting service
4. Update DNS and SSL certificates

## Environment Variables (Optional)

You can also use environment variables:
```bash
export NGROK_URL="https://your-url.ngrok.io/admin/api/"
flutter run -d chrome --dart-define=NGROK_URL=$NGROK_URL
```

Then in your Flutter app:
```dart
const String.fromEnvironment('NGROK_URL', defaultValue: NgrokConfig.ngrokUrl)
```
