<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KFT Video Player</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        }
        
        #player-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: #000;
        }
        
        #vimeo-player {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        #watermark {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            z-index: 1000;
            pointer-events: none;
            transition: all 0.3s ease;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 16px;
            z-index: 999;
        }
        
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        #error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            text-align: center;
            z-index: 999;
            display: none;
        }
        
        .retry-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
            font-size: 14px;
        }
        
        .retry-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div id="player-container">
        <div id="loading">
            <div class="spinner"></div>
            Loading video...
        </div>
        
        <div id="error">
            <h3>Video Unavailable</h3>
            <p>Unable to load the video. Please try again.</p>
            <button class="retry-btn" onclick="retryLoad()">Retry</button>
        </div>
        
        <iframe id="vimeo-player" 
                allow="autoplay; fullscreen; picture-in-picture" 
                allowfullscreen>
        </iframe>
        
        <div id="watermark"></div>
    </div>

    <script src="https://player.vimeo.com/api/player.js"></script>
    <script>
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const vimeoId = urlParams.get('vimeo_id');
        const domain = urlParams.get('domain') || window.location.hostname;
        const autoplay = urlParams.get('autoplay') === '1';
        const userName = urlParams.get('user_name') || '';
        const phoneNumber = urlParams.get('phone_number') || '';
        
        let player = null;
        let watermarkInterval = null;
        
        // Watermark positions
        const positions = [
            { top: '20px', right: '20px', left: 'auto', bottom: 'auto' },
            { top: '20px', left: '20px', right: 'auto', bottom: 'auto' },
            { bottom: '20px', right: '20px', left: 'auto', top: 'auto' },
            { bottom: '20px', left: '20px', right: 'auto', top: 'auto' },
            { top: '50%', right: '20px', left: 'auto', bottom: 'auto' },
            { top: '50%', left: '20px', right: 'auto', bottom: 'auto' }
        ];
        let currentPositionIndex = 0;
        
        function initializePlayer() {
            if (!vimeoId) {
                showError('No video ID provided');
                return;
            }
            
            const iframe = document.getElementById('vimeo-player');
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            
            // Build Vimeo embed URL with domain restrictions
            const embedUrl = `https://player.vimeo.com/video/${vimeoId}?` + 
                `autoplay=${autoplay ? 1 : 0}&` +
                `loop=0&` +
                `title=0&` +
                `byline=0&` +
                `portrait=0&` +
                `controls=1&` +
                `playsinline=1&` +
                `responsive=1&` +
                `dnt=1&` +
                `referrer=${encodeURIComponent(domain)}`;
            
            iframe.src = embedUrl;
            
            // Initialize Vimeo player
            try {
                player = new Vimeo.Player(iframe);
                
                player.ready().then(() => {
                    loading.style.display = 'none';
                    error.style.display = 'none';
                    iframe.style.display = 'block';
                    
                    // Start watermark rotation
                    startWatermark();
                    
                    // Auto-play if requested
                    if (autoplay) {
                        player.play().catch(e => {
                            console.log('Autoplay prevented:', e);
                        });
                    }
                }).catch(e => {
                    console.error('Player ready error:', e);
                    showError('Failed to initialize video player');
                });
                
                player.on('error', (error) => {
                    console.error('Vimeo player error:', error);
                    showError('Video playback error: ' + error.message);
                });
                
                player.on('loaded', () => {
                    console.log('Video loaded successfully');
                });
                
            } catch (e) {
                console.error('Failed to create Vimeo player:', e);
                showError('Failed to create video player');
            }
        }
        
        function showError(message) {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const iframe = document.getElementById('vimeo-player');
            
            loading.style.display = 'none';
            iframe.style.display = 'none';
            error.style.display = 'block';
            error.querySelector('p').textContent = message;
        }
        
        function retryLoad() {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const iframe = document.getElementById('vimeo-player');
            
            error.style.display = 'none';
            loading.style.display = 'block';
            iframe.style.display = 'none';
            
            // Retry after a short delay
            setTimeout(initializePlayer, 1000);
        }
        
        function startWatermark() {
            const watermark = document.getElementById('watermark');
            
            if (userName || phoneNumber) {
                const displayText = userName || phoneNumber;
                watermark.textContent = displayText;
                watermark.style.display = 'block';
                
                // Move watermark every 49 seconds
                watermarkInterval = setInterval(() => {
                    currentPositionIndex = (currentPositionIndex + 1) % positions.length;
                    const pos = positions[currentPositionIndex];
                    
                    Object.assign(watermark.style, pos);
                }, 49000);
            }
        }
        
        // Expose player to parent window for external control
        window.vimeoPlayer = player;
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializePlayer);
        
        // Cleanup on unload
        window.addEventListener('beforeunload', () => {
            if (watermarkInterval) {
                clearInterval(watermarkInterval);
            }
        });
    </script>
</body>
</html>
