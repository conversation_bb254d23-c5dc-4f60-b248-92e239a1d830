import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../widgets/pwa_install_button.dart';
import 'enhanced_login_page.dart';

class PWALoginWrapper extends StatefulWidget {
  const PWALoginWrapper({Key? key}) : super(key: key);

  @override
  State<PWALoginWrapper> createState() => _PWALoginWrapperState();
}

class _PWALoginWrapperState extends State<PWALoginWrapper> {
  bool _showPWADialog = true;
  bool _pwaInstallCompleted = false;

  @override
  void initState() {
    super.initState();
    
    // On non-web platforms, skip PWA dialog
    if (!kIsWeb) {
      _showPWADialog = false;
    }
  }

  void _onInstallComplete() {
    setState(() {
      _pwaInstallCompleted = true;
      _showPWADialog = false;
    });
  }

  void _onSkipInstall() {
    setState(() {
      _showPWADialog = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Handle back button properly
        if (_showPWADialog) {
          // If PWA dialog is showing, skip it and go to login
          _onSkipInstall();
          return false; // Don't exit app
        }
        return true; // Allow normal back navigation
      },
      child: Scaffold(
        body: Stack(
          children: [
            // Login Page (always present but may be covered by dialog)
            const EnhancedLoginPage(),

            // PWA Install Dialog Overlay
            if (_showPWADialog && kIsWeb)
              PWAInstallDialog(
                onInstallComplete: _onInstallComplete,
                onSkip: _onSkipInstall,
              ),
          ],
        ),
      ),
    );
  }
}
