import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../widgets/pwa_enforcement_dialog.dart';
import '../services/user_service.dart';
import '../services/persistent_auth_service.dart';
// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html;
import 'package:js/js_util.dart' as js_util;

class PWAEnforcedHome extends StatefulWidget {
  final Widget child;

  const PWAEnforcedHome({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<PWAEnforcedHome> createState() => _PWAEnforcedHomeState();
}

class _PWAEnforcedHomeState extends State<PWAEnforcedHome> {
  bool _showEnforcementDialog = false;
  bool _isCheckingPWA = true;

  @override
  void initState() {
    super.initState();
    _checkPWAStatus();
  }

  void _checkPWAStatus() async {
    if (!kIsWeb) {
      // On non-web platforms, allow access
      setState(() {
        _isCheckingPWA = false;
      });
      return;
    }

    try {
      // Check if running as PWA
      final isPWA = js_util.callMethod(html.window, 'isPWA', []);
      if (isPWA == true) {
        // Already running as PWA, allow access
        setState(() {
          _isCheckingPWA = false;
        });
      } else {
        // Not running as PWA, show enforcement dialog
        setState(() {
          _showEnforcementDialog = true;
          _isCheckingPWA = false;
        });
      }
    } catch (e) {
      print('Error checking PWA status: $e');
      // If there's an error, assume not PWA and show dialog
      setState(() {
        _showEnforcementDialog = true;
        _isCheckingPWA = false;
      });
    }
  }

  void _onInstallComplete() {
    setState(() {
      _showEnforcementDialog = false;
    });
  }

  void _onLogout() async {
    try {
      // Clear authentication
      await UserService().logout();
      await PersistentAuthService().logout();

      if (mounted) {
        // Navigate back to login
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/login',
          (route) => false,
        );
      }
    } catch (e) {
      print('Error during logout: $e');
      // Force navigation even if logout fails
      if (mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/login',
          (route) => false,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isCheckingPWA) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Checking app status...'),
            ],
          ),
        ),
      );
    }

    return Stack(
      children: [
        // Main app content
        widget.child,
        
        // PWA enforcement dialog overlay
        if (_showEnforcementDialog)
          PWAEnforcementDialog(
            onInstallComplete: _onInstallComplete,
            onLogout: _onLogout,
          ),
      ],
    );
  }
}
