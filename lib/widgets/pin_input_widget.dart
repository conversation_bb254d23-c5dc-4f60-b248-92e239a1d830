import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/kft_design_system.dart';
import 'package:flutter/rendering.dart';

class PinInputWidget extends StatefulWidget {
  final Function(String) onPinCompleted;
  final Function(String)? onPinChanged;
  final bool isLoading;
  final String? errorMessage;
  final int pinLength;
  final bool autoFocus;

  const PinInputWidget({
    Key? key,
    required this.onPinCompleted,
    this.onPinChanged,
    this.isLoading = false,
    this.errorMessage,
    this.pinLength = 4,
    this.autoFocus = true,
  }) : super(key: key);

  @override
  State<PinInputWidget> createState() => _PinInputWidgetState();
}

class _PinInputWidgetState extends State<PinInputWidget>
    with TickerProviderStateMixin {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;
  late AnimationController _shakeController;
  late AnimationController _pulseController;
  late Animation<double> _shakeAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      widget.pinLength,
      (index) => TextEditingController(),
    );
    _focusNodes = List.generate(
      widget.pinLength,
      (index) => FocusNode(),
    );

    // Add focus listeners for clipboard auto-detect
    for (int i = 0; i < widget.pinLength; i++) {
      _focusNodes[i].addListener(() async {
        if (_focusNodes[i].hasFocus) {
          // Check clipboard when any field is focused
          try {
            final data = await Clipboard.getData('text/plain');
            final text = data?.text ?? '';

            // Only auto-fill if clipboard contains valid PIN and fields are mostly empty
            if (text.length == widget.pinLength &&
                RegExp(r'^\d+$').hasMatch(text) &&
                _controllers.where((c) => c.text.isNotEmpty).length <= 1) {

              // Clear all fields first
              for (int j = 0; j < widget.pinLength; j++) {
                _controllers[j].clear();
              }

              // Fill with clipboard content
              for (int j = 0; j < widget.pinLength; j++) {
                _controllers[j].text = text[j];
              }

              // Focus the last field for easy editing
              _focusNodes[widget.pinLength - 1].requestFocus();
              widget.onPinChanged?.call(text);
              widget.onPinCompleted(text);
            }
          } catch (e) {
            // Ignore clipboard errors
            debugPrint('Clipboard access error: $e');
          }
        }
      });
    }

    // Initialize animations
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _shakeAnimation = Tween<double>(
      begin: 0,
      end: 10,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticIn,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Auto focus first field
    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNodes[0].requestFocus();
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    _shakeController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(PinInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Trigger shake animation on error
    if (widget.errorMessage != null &&
        widget.errorMessage != oldWidget.errorMessage &&
        widget.errorMessage!.isNotEmpty) {
      _triggerShakeAnimation();
    }
  }

  void _triggerShakeAnimation() {
    _shakeController.reset();
    _shakeController.forward().then((_) {
      _shakeController.reverse();
    });
  }

  void _triggerPulseAnimation() {
    _pulseController.reset();
    _pulseController.forward().then((_) {
      _pulseController.reverse();
    });
  }

  void _onChanged(String value, int index) {
    // Handle paste: if value is multi-digit and matches pin length, distribute
    if (value.length > 1 && RegExp(r'^\d+$').hasMatch(value)) {
      // Distribute digits across all fields
      for (int i = 0; i < widget.pinLength; i++) {
        if (i < value.length) {
          _controllers[i].text = value[i];
        } else {
          _controllers[i].clear();
        }
      }
      // Unfocus last field
      _focusNodes[widget.pinLength - 1].unfocus();
      final pin = _controllers.map((c) => c.text).join();
      widget.onPinChanged?.call(pin);
      if (pin.length == widget.pinLength) {
        widget.onPinCompleted(pin);
      }
      return;
    }

    if (value.isNotEmpty) {
      // Move to next field
      if (index < widget.pinLength - 1) {
        _focusNodes[index + 1].requestFocus();
      } else {
        // Last field, unfocus
        _focusNodes[index].unfocus();
      }
    }

    // Check if PIN is complete
    String currentPin = _controllers.map((c) => c.text).join();
    widget.onPinChanged?.call(currentPin);

    if (currentPin.length == widget.pinLength) {
      _triggerPulseAnimation();
      widget.onPinCompleted(currentPin);
    }
  }

  void _onKeyEvent(RawKeyEvent event, int index) {
    if (event is RawKeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.backspace) {
        if (_controllers[index].text.isEmpty && index > 0) {
          // Move to previous field and clear it
          _focusNodes[index - 1].requestFocus();
          _controllers[index - 1].clear();
        }
      }
    }
  }

  void clearPin() {
    for (var controller in _controllers) {
      controller.clear();
    }
    _focusNodes[0].requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasError = widget.errorMessage != null && widget.errorMessage!.isNotEmpty;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // PIN Input Fields
        AnimatedBuilder(
          animation: _shakeAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(_shakeAnimation.value, 0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(widget.pinLength, (index) {
                  return AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Container(
                        margin: EdgeInsets.only(
                          right: index < widget.pinLength - 1 ? 14 : 0,
                        ),
                        child: Transform.scale(
                          scale: _pulseAnimation.value,
                          child: _buildPinField(context, index, hasError),
                        ),
                      );
                    },
                  );
                }),
              ),
            );
          },
        ),

        // Error Message
        if (hasError) ...[
          const SizedBox(height: 20),
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFFFEF2F2),
                  const Color(0xFFFECACA).withOpacity(0.3),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFDC2626).withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFDC2626).withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: const Color(0xFFDC2626),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.error_outline,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    widget.errorMessage!,
                    style: const TextStyle(
                      color: Color(0xFFDC2626),
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],

        // Loading Indicator
        if (widget.isLoading) ...[
          const SizedBox(height: 28),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF6366F1).withOpacity(0.1),
                  const Color(0xFF8B5CF6).withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFF6366F1).withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const CircularProgressIndicator(
                    strokeWidth: 2.5,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 16),
                const Text(
                  'Verifying PIN...',
                  style: TextStyle(
                    color: Color(0xFF6366F1),
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPinField(BuildContext context, int index, bool hasError) {
    final theme = Theme.of(context);
    final isActive = _focusNodes[index].hasFocus;
    final hasValue = _controllers[index].text.isNotEmpty;

    return Container(
      width: 56,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: hasError
            ? LinearGradient(
                colors: [
                  const Color(0xFFFEF2F2),
                  const Color(0xFFFECACA).withOpacity(0.3),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : isActive
                ? LinearGradient(
                    colors: [
                      const Color(0xFF6366F1).withOpacity(0.1),
                      const Color(0xFF8B5CF6).withOpacity(0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : hasValue
                    ? LinearGradient(
                        colors: [
                          const Color(0xFFF0F9FF),
                          const Color(0xFFE0F2FE),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      )
                    : LinearGradient(
                        colors: [
                          const Color(0xFFF9FAFB),
                          const Color(0xFFF3F4F6),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
        border: Border.all(
          color: hasError
              ? const Color(0xFFDC2626)
              : isActive
                  ? const Color(0xFF6366F1)
                  : hasValue
                      ? const Color(0xFF0284C7)
                      : const Color(0xFFE5E7EB),
          width: hasError || isActive ? 2.0 : 1.5,
        ),
        boxShadow: [
          if (isActive) ...[
            BoxShadow(
              color: const Color(0xFF6366F1).withOpacity(0.25),
              blurRadius: 8,
              offset: const Offset(0, 3),
              spreadRadius: 0,
            ),
          ] else if (hasValue) ...[
            BoxShadow(
              color: const Color(0xFF0284C7).withOpacity(0.15),
              blurRadius: 6,
              offset: const Offset(0, 2),
              spreadRadius: 0,
            ),
          ] else ...[
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 1),
              spreadRadius: 0,
            ),
          ],
        ],
      ),
      child: RawKeyboardListener(
        focusNode: FocusNode(),
        onKey: (event) => _onKeyEvent(event, index),
        child: TextField(
          controller: _controllers[index],
          focusNode: _focusNodes[index],
          textAlign: TextAlign.center,
          keyboardType: TextInputType.number,
          maxLength: 1,
          enabled: !widget.isLoading,
          obscureText: false, // Make OTP/PIN visible for user convenience
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: hasError
                ? const Color(0xFFDC2626)
                : hasValue
                    ? const Color(0xFF1F2937)
                    : const Color(0xFF6B7280),
            letterSpacing: 0.5,
            height: 1.0,
          ),
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
          ],
          decoration: InputDecoration(
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
            errorBorder: InputBorder.none,
            focusedErrorBorder: InputBorder.none,
            counterText: '',
            contentPadding: EdgeInsets.zero,
            fillColor: Colors.transparent,
            filled: false,
            hintText: null,
            labelText: null,
          ),
          onChanged: (value) => _onChanged(value, index),
        ),
      ),
    );
  }
}
