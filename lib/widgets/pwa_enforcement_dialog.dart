import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:async';
// Only import dart:html on web
// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html;
import 'package:js/js_util.dart' as js_util;

class PWAEnforcementDialog extends StatefulWidget {
  final VoidCallback onInstallComplete;
  final VoidCallback onLogout;

  const PWAEnforcementDialog({
    Key? key,
    required this.onInstallComplete,
    required this.onLogout,
  }) : super(key: key);

  @override
  State<PWAEnforcementDialog> createState() => _PWAEnforcementDialogState();
}

class _PWAEnforcementDialogState extends State<PWAEnforcementDialog> 
    with SingleTickerProviderStateMixin {
  bool _isInstallAvailable = false;
  StreamSubscription? _eventSub;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (kIsWeb) {
      _eventSub = html.window.on["pwa-install-available"].listen((event) {
        setState(() {
          _isInstallAvailable = true;
        });
      });
      
      // Check if install prompt is already available
      _checkInstallAvailability();
    }
    
    _animationController.forward();
  }

  void _checkInstallAvailability() {
    Timer(const Duration(milliseconds: 500), () {
      if (kIsWeb && mounted) {
        try {
          final isInstallable = js_util.callMethod(html.window, 'isPWAInstallable', []);
          if (isInstallable == true) {
            setState(() {
              _isInstallAvailable = true;
            });
          }
        } catch (e) {
          print('Error checking PWA installability: $e');
        }
      }
    });
  }

  @override
  void dispose() {
    _eventSub?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _installPWA() async {
    if (kIsWeb) {
      js_util.callMethod(html.window, 'installPWA', []);
      
      // Wait a moment for the install process
      await Future.delayed(const Duration(milliseconds: 1500));
      widget.onInstallComplete();
    }
  }

  void _logout() {
    widget.onLogout();
  }

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb) {
      // On non-web platforms, allow access
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onInstallComplete();
      });
      return const SizedBox.shrink();
    }

    return WillPopScope(
      onWillPop: () async => false, // Prevent back button
      child: Material(
        color: Colors.black.withOpacity(0.9),
        child: Center(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Opacity(
                  opacity: _fadeAnimation.value,
                  child: Container(
                    margin: const EdgeInsets.all(32),
                    padding: const EdgeInsets.all(32),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // App Icon
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0xFF6366F1).withOpacity(0.3),
                                blurRadius: 12,
                                offset: const Offset(0, 6),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.security,
                            color: Colors.white,
                            size: 40,
                          ),
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Title
                        const Text(
                          'App Installation Required',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF1F2937),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Description
                        const Text(
                          'For security and optimal performance, you must install the KFT Fitness app to continue using our services.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Color(0xFF6B7280),
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 32),
                        
                        // Buttons
                        if (_isInstallAvailable) ...[
                          // Install Button
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _installPWA,
                              icon: const Icon(Icons.download, color: Colors.white),
                              label: const Text(
                                'Install App Now',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF6366F1),
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 0,
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 12),
                          
                          // Logout Button
                          SizedBox(
                            width: double.infinity,
                            child: TextButton(
                              onPressed: _logout,
                              child: const Text(
                                'Logout',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF6B7280),
                                ),
                              ),
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                          ),
                        ] else ...[
                          // Loading state
                          const CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6366F1)),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Checking installation availability...',
                            style: TextStyle(
                              color: Color(0xFF6B7280),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
