import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../models/course_video.dart';
import '../utils/video_security_helper.dart';
import '../services/api_service.dart';
import '../services/user_service.dart';
import 'dynamic_video_watermark.dart';
import '../services/vimeo_pro_auth_service.dart';
import '../services/hosted_player_service.dart';
import '../services/robust_seek_service.dart';
import '../services/progress_service.dart';
import 'dart:convert';

/// Domain-Restricted Vimeo Player - Handles Vimeo domain restrictions automatically
/// Uses proper referrer headers and domain verification for seamless playback
class SimpleVimeoPlayer extends StatefulWidget {
  final CourseVideo video;
  final bool autoPlay;
  final VoidCallback? onReady;
  final VoidCallback? onPlay;
  final VoidCallback? onPause;
  final VoidCallback? onCompleted;
  final Function(int position)? onProgress;
  final Function(String error)? onError;
  final VoidCallback? onEnterFullscreen;
  final VoidCallback? onExitFullscreen;

  const SimpleVimeoPlayer({
    Key? key,
    required this.video,
    this.autoPlay = true,
    this.onReady,
    this.onPlay,
    this.onPause,
    this.onCompleted,
    this.onProgress,
    this.onError,
    this.onEnterFullscreen,
    this.onExitFullscreen,
  }) : super(key: key);

  @override
  State<SimpleVimeoPlayer> createState() => SimpleVimeoPlayerState();
}

class SimpleVimeoPlayerState extends State<SimpleVimeoPlayer> with SingleTickerProviderStateMixin {
  WebViewController? _preloadController;
  bool _showPlayer = false;
  bool _isPreloaded = false;
  late final String _playerUrl;
  bool _isFullscreen = false;
  String _userName = '';
  String _phoneNumber = '';

  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  late WebViewController _webViewController;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _loadUserInfo();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 350),
    );
    _scaleAnimation = Tween<double>(begin: 0.97, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _animationController.value = 1.0;
  }

  Future<void> _loadUserInfo() async {
    try {
      final userService = UserService();
      final profile = await userService.getUserProfile();
      if (profile != null && mounted) {
        setState(() {
          _userName = profile.name ?? '';
          _phoneNumber = profile.phoneNumber ?? '';
        });
      }
    } catch (e) {
      debugPrint('Error loading user info for watermark: $e');
    }
  }

  @override
  void dispose() {
    // Restore overlays and orientation for extra reliability
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    _animationController.dispose();
    super.dispose();
  }

  void resetAndPlay() {
    if (_webViewController != null) {
      _webViewController.runJavaScript('''
        if (window.vimeoPlayer) {
          window.vimeoPlayer.loadVideo('${widget.video.videoUrl}').then(function() {
            window.vimeoPlayer.play();
          });
        }
      ''');
    }
  }

  Future<void> _initializePlayer() async {
    final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
    _playerUrl = 'https://mycloudforge.com/kft_vimeo_player.html?vimeo_id=$vimeoId&domain=mycloudforge.com&autoplay=1';
    _preloadController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(NavigationDelegate(
        onPageFinished: (_) {
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) setState(() => _isPreloaded = true);
          });
        },
      ));
    _preloadController!.loadRequest(Uri.parse(_playerUrl));
  }

  Future<void> _toggleFullscreen() async {
    if (_isFullscreen) {
      // Animate out
      await _animationController.reverse();
      // Exit fullscreen: portrait + show overlays
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
      if (mounted) setState(() => _isFullscreen = false);
      await _animationController.forward();
      widget.onExitFullscreen?.call();
    } else {
      // Animate in
      await _animationController.reverse();
      // Enter fullscreen: landscape + hide overlays
      _forceImmersiveMode();
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      // Extra reliability: re-apply immersive mode after a short delay
      Future.delayed(const Duration(milliseconds: 300), () {
        _forceImmersiveMode();
      });
      if (mounted) setState(() => _isFullscreen = true);
      await _animationController.forward();
      widget.onEnterFullscreen?.call();
    }
  }

  void _forceImmersiveMode() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    Future.delayed(const Duration(milliseconds: 300), () {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    });
  }

  Future<bool> handleBackPressed() async {
    if (_isFullscreen) {
      await _toggleFullscreen();
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Material(
          color: Colors.black.withOpacity(0.9 * _opacityAnimation.value),
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: child,
          ),
        );
      },
      child: Stack(
      children: [
        // Always show the WebViewWidget
        WebViewWidget(controller: _preloadController!),
        // Play button overlay
        if (!_showPlayer)
          Positioned.fill(
            child: Container(
              color: Colors.black.withOpacity(0.3),
              child: Center(
                child: GestureDetector(
                  onTap: () => setState(() => _showPlayer = true),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      shape: BoxShape.circle,
                    ),
                    padding: const EdgeInsets.all(24),
                    child: Icon(Icons.play_circle, size: 80, color: Colors.white),
                  ),
                ),
              ),
            ),
          ),
        // Fullscreen toggle button (top right, small, clean, no bg)
        if (_showPlayer)
          Positioned(
            top: 12,
            right: 12,
            child: GestureDetector(
              onTap: _toggleFullscreen,
              child: Icon(
                _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
                size: 28,
              color: Colors.white,
              ),
            ),
          ),
        // Dynamic watermark
        if (_showPlayer && _userName.isNotEmpty)
          DynamicVideoWatermark(
            userName: _userName,
            phoneNumber: _phoneNumber,
            isVisible: true,
            opacity: 0.3,
          ),
      ],
      ),
    );
  }

  // Add this method for compatibility with orientation/state restoration
  void restoreVideoPosition() {
    // No-op for now (preload player does not support position restore yet)
  }
}
