import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'dart:async';
// Only import dart:html on web
// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html;
import 'package:js/js_util.dart' as js_util;

class PWAInstallButton extends StatefulWidget {
  const PWAInstallButton({Key? key}) : super(key: key);

  @override
  State<PWAInstallButton> createState() => _PWAInstallButtonState();
}

class _PWAInstallButtonState extends State<PWAInstallButton> {
  bool _isInstallAvailable = false;
  StreamSubscription? _eventSub;

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      _eventSub = html.window.on["pwa-install-available"].listen((event) {
        setState(() {
          _isInstallAvailable = true;
        });
      });
    }
  }

  @override
  void dispose() {
    _eventSub?.cancel();
    super.dispose();
  }

  void _installPWA() {
    if (kIsWeb) {
      js_util.callMethod(html.window, 'installPWA', []);
      setState(() {
        _isInstallAvailable = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb || !_isInstallAvailable) return SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: ElevatedButton.icon(
        icon: Icon(Icons.download),
        label: Text('Install App'),
        onPressed: _installPWA,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }
}

class PWAInstallDialog extends StatefulWidget {
  final VoidCallback onInstallComplete;
  final VoidCallback onSkip;

  const PWAInstallDialog({
    Key? key,
    required this.onInstallComplete,
    required this.onSkip,
  }) : super(key: key);

  @override
  State<PWAInstallDialog> createState() => _PWAInstallDialogState();
}

class _PWAInstallDialogState extends State<PWAInstallDialog>
    with SingleTickerProviderStateMixin {
  bool _isInstallAvailable = false;
  StreamSubscription? _eventSub;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (kIsWeb) {
      _eventSub = html.window.on["pwa-install-available"].listen((event) {
        setState(() {
          _isInstallAvailable = true;
        });
        _animationController.forward();
      });

      // Check if install prompt is already available
      _checkInstallAvailability();
    }
  }

  void _checkInstallAvailability() {
    // Check if already running as PWA
    if (kIsWeb) {
      try {
        final isPWA = js_util.callMethod(html.window, 'isPWA', []);
        if (isPWA == true) {
          // Already installed as PWA, skip dialog
          Timer(const Duration(milliseconds: 100), () {
            if (mounted) {
              widget.onSkip();
            }
          });
          return;
        }
      } catch (e) {
        print('Error checking PWA status: $e');
      }
    }

    // Trigger a check for install availability
    Timer(const Duration(milliseconds: 1000), () {
      if (kIsWeb && mounted) {
        try {
          // Check if install prompt is available
          final isInstallable = js_util.callMethod(html.window, 'isPWAInstallable', []);
          if (isInstallable == true) {
            setState(() {
              _isInstallAvailable = true;
            });
            _animationController.forward();
          } else {
            // If not available, skip to login after a short delay
            Timer(const Duration(seconds: 1), () {
              if (mounted) {
                widget.onSkip();
              }
            });
          }
        } catch (e) {
          print('Error checking PWA installability: $e');
          // If there's an error, just skip to login
          Timer(const Duration(milliseconds: 500), () {
            if (mounted) {
              widget.onSkip();
            }
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _eventSub?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _installPWA() async {
    if (kIsWeb) {
      js_util.callMethod(html.window, 'installPWA', []);
      setState(() {
        _isInstallAvailable = false;
      });

      // Wait a moment for the install process
      await Future.delayed(const Duration(milliseconds: 1000));
      widget.onInstallComplete();
    }
  }

  void _skipInstall() {
    widget.onSkip();
  }

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb) {
      // On non-web platforms, skip directly to login
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onSkip();
      });
      return const SizedBox.shrink();
    }

    return Material(
      color: Colors.black.withOpacity(0.8),
      child: Center(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Opacity(
                opacity: _fadeAnimation.value,
                child: Container(
                  margin: const EdgeInsets.all(32),
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // App Icon
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF6366F1).withOpacity(0.3),
                              blurRadius: 12,
                              offset: const Offset(0, 6),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.fitness_center,
                          color: Colors.white,
                          size: 40,
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Title
                      const Text(
                        'Install KFT Fitness App',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1F2937),
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 16),

                      // Description
                      const Text(
                        'Get the full app experience with offline access, push notifications, and faster loading times.',
                        style: TextStyle(
                          fontSize: 16,
                          color: Color(0xFF6B7280),
                          height: 1.5,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 32),

                      // Buttons
                      if (_isInstallAvailable) ...[
                        // Install Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _installPWA,
                            icon: const Icon(Icons.download, color: Colors.white),
                            label: const Text(
                              'Install App',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF6366F1),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 0,
                            ),
                          ),
                        ),

                        const SizedBox(height: 12),

                        // Skip Button
                        SizedBox(
                          width: double.infinity,
                          child: TextButton(
                            onPressed: _skipInstall,
                            child: const Text(
                              'Continue in Browser',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF6B7280),
                              ),
                            ),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                      ] else ...[
                        // Loading state
                        const CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6366F1)),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Checking installation availability...',
                          style: TextStyle(
                            color: Color(0xFF6B7280),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}