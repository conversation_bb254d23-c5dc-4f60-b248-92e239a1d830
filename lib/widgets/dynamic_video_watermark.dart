import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';

class DynamicVideoWatermark extends StatefulWidget {
  final String userName;
  final String phoneNumber;
  final bool isVisible;
  final double opacity;

  const DynamicVideoWatermark({
    Key? key,
    required this.userName,
    required this.phoneNumber,
    this.isVisible = true,
    this.opacity = 0.3,
  }) : super(key: key);

  @override
  State<DynamicVideoWatermark> createState() => _DynamicVideoWatermarkState();
}

class _DynamicVideoWatermarkState extends State<DynamicVideoWatermark>
    with SingleTickerProviderStateMixin {
  Timer? _positionTimer;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Position variables
  WatermarkPosition _currentPosition = WatermarkPosition.bottomRight;
  final Random _random = Random();
  
  // Available positions
  final List<WatermarkPosition> _positions = [
    WatermarkPosition.topLeft,
    WatermarkPosition.topRight,
    WatermarkPosition.bottomLeft,
    WatermarkPosition.bottomRight,
    WatermarkPosition.centerLeft,
    WatermarkPosition.centerRight,
  ];

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: widget.opacity,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Start the animation
    _animationController.forward();
    
    // Start position changing timer (49 seconds)
    _startPositionTimer();
  }

  void _startPositionTimer() {
    _positionTimer = Timer.periodic(const Duration(seconds: 49), (timer) {
      if (mounted) {
        _changePosition();
      }
    });
  }

  void _changePosition() {
    // Fade out
    _animationController.reverse().then((_) {
      if (mounted) {
        setState(() {
          // Get a random position different from current
          WatermarkPosition newPosition;
          do {
            newPosition = _positions[_random.nextInt(_positions.length)];
          } while (newPosition == _currentPosition && _positions.length > 1);
          
          _currentPosition = newPosition;
        });
        
        // Fade in with new position
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _positionTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible || widget.userName.isEmpty) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Positioned(
          top: _currentPosition.getTop(context),
          left: _currentPosition.getLeft(context),
          right: _currentPosition.getRight(context),
          bottom: _currentPosition.getBottom(context),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.userName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    widget.phoneNumber,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

enum WatermarkPosition {
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
  centerLeft,
  centerRight,
}

extension WatermarkPositionExtension on WatermarkPosition {
  double? getTop(BuildContext context) {
    switch (this) {
      case WatermarkPosition.topLeft:
      case WatermarkPosition.topRight:
        return 20;
      case WatermarkPosition.centerLeft:
      case WatermarkPosition.centerRight:
        return MediaQuery.of(context).size.height * 0.4;
      default:
        return null;
    }
  }

  double? getLeft(BuildContext context) {
    switch (this) {
      case WatermarkPosition.topLeft:
      case WatermarkPosition.bottomLeft:
      case WatermarkPosition.centerLeft:
        return 20;
      default:
        return null;
    }
  }

  double? getRight(BuildContext context) {
    switch (this) {
      case WatermarkPosition.topRight:
      case WatermarkPosition.bottomRight:
      case WatermarkPosition.centerRight:
        return 20;
      default:
        return null;
    }
  }

  double? getBottom(BuildContext context) {
    switch (this) {
      case WatermarkPosition.bottomLeft:
      case WatermarkPosition.bottomRight:
        return 20;
      default:
        return null;
    }
  }
}
