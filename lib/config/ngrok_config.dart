import '../services/api_service.dart';

class NgrokConfig {
  // Set your ngrok URL here when you start ngrok
  // Example: https://abc123.ngrok.io/admin/api/
  // TODO: Replace with your actual ngrok URL
  static const String ngrokUrl = 'https://your-ngrok-url.ngrok.io/admin/api/';
  
  // Production URL fallback
  static const String productionUrl = 'https://mycloudforge.com/admin/api/';
  
  // Development localhost URL
  static const String localhostUrl = 'http://localhost:8080/admin/api/';
  
  /// Initialize API service with ngrok URL
  static void initializeWithNgrok({String? customNgrokUrl}) {
    final url = customNgrokUrl ?? ngrokUrl;
    
    if (url.contains('your-ngrok-url')) {
      print('⚠️ Please update NgrokConfig.ngrokUrl with your actual ngrok URL');
      print('💡 Using production URL as fallback: $productionUrl');
      ApiService.setBaseUrl(productionUrl);
    } else {
      print('🚀 Initializing API service with ngrok URL: $url');
      ApiService.setBaseUrl(url);
    }
  }
  
  /// Initialize API service with production URL
  static void initializeWithProduction() {
    print('🌐 Initializing API service with production URL: $productionUrl');
    ApiService.setBaseUrl(productionUrl);
  }
  
  /// Initialize API service with localhost URL
  static void initializeWithLocalhost() {
    print('🏠 Initializing API service with localhost URL: $localhostUrl');
    ApiService.setBaseUrl(localhostUrl);
  }
  
  /// Auto-detect and initialize the best available URL
  static Future<void> autoInitialize() async {
    // Try ngrok first if configured
    if (!ngrokUrl.contains('your-ngrok-url')) {
      try {
        // Test ngrok URL
        final response = await ApiService.httpClient.get(
          Uri.parse('${ngrokUrl}health'),
        ).timeout(const Duration(seconds: 3));
        
        if (response.statusCode == 200) {
          initializeWithNgrok();
          return;
        }
      } catch (e) {
        print('❌ Ngrok URL not reachable: $e');
      }
    }
    
    // Fallback to production
    initializeWithProduction();
  }
  
  /// Update ngrok URL at runtime
  static void updateNgrokUrl(String newUrl) {
    if (!newUrl.endsWith('/admin/api/')) {
      if (!newUrl.endsWith('/')) {
        newUrl = '$newUrl/';
      }
      newUrl = '${newUrl}admin/api/';
    }
    
    print('🔄 Updating ngrok URL to: $newUrl');
    ApiService.setBaseUrl(newUrl);
  }
}

/// Extension to easily switch between different API endpoints
extension ApiServiceNgrok on ApiService {
  /// Quick method to switch to ngrok
  static void useNgrok([String? customUrl]) {
    NgrokConfig.initializeWithNgrok(customNgrokUrl: customUrl);
  }
  
  /// Quick method to switch to production
  static void useProduction() {
    NgrokConfig.initializeWithProduction();
  }
  
  /// Quick method to switch to localhost
  static void useLocalhost() {
    NgrokConfig.initializeWithLocalhost();
  }
}
