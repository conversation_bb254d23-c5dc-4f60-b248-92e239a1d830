import '../services/api_service.dart';

String getFullImageUrl(String? url) {
  if (url == null || url.isEmpty) return '';
  if (url.startsWith('http://') || url.startsWith('https://')) return url;

  String baseUrl = ApiService.baseUrl.replaceAll('/api', '');
  if (baseUrl.endsWith('/')) {
    baseUrl = baseUrl.substring(0, baseUrl.length - 1);
  }

  String cleanPath = url;
  if (cleanPath.startsWith('/')) {
    cleanPath = cleanPath.substring(1);
  }

  // Return clean URL without cache busting for better performance
  return '$baseUrl/$cleanPath';
}