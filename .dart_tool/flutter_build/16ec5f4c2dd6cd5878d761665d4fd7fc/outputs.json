["/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/assets/images/logo.webp", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/hosted_vimeo_player.html", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/packages/awesome_notifications/test/assets/images/test_image.png", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/packages/flutter_inappwebview_web/assets/web/web_support.js", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/packages/wakelock_plus/assets/no_sleep.js", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "/Users/<USER>/Desktop/64/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json"]